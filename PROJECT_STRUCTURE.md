# VerifID Project Structure

This document provides a detailed overview of the VerifID project structure and organization.

## 📁 Root Directory

```
VerifID/
├── README.md                 # Main project documentation
├── LICENSE                   # MIT license file
├── CONTRIBUTING.md          # Contribution guidelines
├── PROJECT_STRUCTURE.md     # This file
├── setup.sh                 # Unix/Linux setup script
├── setup.bat                # Windows setup script
├── .gitignore              # Git ignore rules
├── frontend/               # React frontend application
├── flask_backend/          # Flask backend API
├── face_info/             # Stored face encodings (created at runtime)
└── uploads/               # Temporary file uploads (created at runtime)
```

## 🎨 Frontend Structure (`frontend/`)

```
frontend/
├── package.json            # Node.js dependencies and scripts
├── package-lock.json       # Locked dependency versions
├── vite.config.js         # Vite build configuration
├── eslint.config.js       # ESLint configuration
├── index.html             # Main HTML template
├── README.md              # Frontend-specific documentation
├── public/                # Static assets
│   ├── logo.png          # Application logo
│   └── favicon.ico       # Browser favicon
└── src/                   # Source code
    ├── main.jsx          # Application entry point
    ├── App.jsx           # Main App component
    ├── components/       # Reusable UI components
    │   ├── Navbar.jsx   # Navigation bar component
    │   └── Footer.jsx   # Footer component (if exists)
    ├── pages/            # Page components
    │   ├── HomePage.jsx         # Landing page
    │   ├── LoginPage.jsx        # User login
    │   ├── RegisterPage.jsx     # User registration
    │   ├── ProfilePage.jsx      # User profile management
    │   ├── VerificationPage.jsx # ID document verification
    │   ├── VideoVerification.jsx # Face liveness verification
    │   └── NotFound.jsx         # 404 error page
    ├── styles/           # SCSS stylesheets
    │   ├── global.css           # Global styles
    │   ├── Navbar.module.scss   # Navbar component styles
    │   ├── HomePage.module.scss # Home page styles
    │   ├── LoginPage.module.scss # Login page styles
    │   ├── RegisterPage.module.scss # Register page styles
    │   ├── ProfilePage.module.scss # Profile page styles
    │   ├── VerificationPage.module.scss # Verification page styles
    │   └── NotFound.module.scss # 404 page styles
    ├── utils/            # Utility functions
    │   ├── auth.js      # Authentication helpers
    │   ├── api.js       # API communication
    │   └── validation.js # Form validation
    └── contexts/         # React contexts
        └── AuthContext.jsx # Authentication context
```

## 🔧 Backend Structure (`flask_backend/`)

```
flask_backend/
├── app.py                  # Main Flask application
├── requirements.txt        # Python dependencies
├── .env                   # Environment variables (created by setup)
├── README.md              # Backend-specific documentation
├── liveness_service.py    # Face verification and liveness detection
├── ocr_utils.py          # Document OCR processing
├── uploads/              # Temporary file storage (created at runtime)
├── temp/                 # Temporary processing files (created at runtime)
└── __pycache__/          # Python bytecode cache (created at runtime)
```

## 📊 Data Storage

### Face Encodings (`face_info/`)
```
face_info/
├── {user_id_1}.jpg       # User 1 face encoding image
├── {user_id_2}.jpg       # User 2 face encoding image
└── ...                   # Additional user face images
```

### Temporary Uploads (`uploads/`)
```
uploads/
├── temp_id_document_1.jpg    # Temporary ID document uploads
├── temp_id_document_2.png    # (Automatically cleaned up)
└── ...
```

## 🔄 Data Flow

### 1. User Registration Flow
```
Frontend (RegisterPage) 
    → Backend (/api/auth/register) 
    → User Database Storage
    → Response to Frontend
```

### 2. ID Document Verification Flow
```
Frontend (VerificationPage) 
    → File Upload to Backend (/api/verify/id)
    → OCR Processing (ocr_utils.py)
    → Face Extraction
    → Data Validation
    → Storage in face_info/
    → Response to Frontend
```

### 3. Face Liveness Verification Flow
```
Frontend (VideoVerification) 
    → WebSocket Connection
    → Real-time Video Frames
    → Backend (liveness_service.py)
    → Face Detection & Movement Analysis
    → Face Matching with Stored Encoding
    → Real-time Feedback to Frontend
```

## 🛠️ Key Technologies by Component

### Frontend Technologies
- **React 19.0.0**: UI framework
- **React Router DOM**: Client-side routing
- **Socket.IO Client**: Real-time communication
- **Sass**: CSS preprocessing
- **Vite**: Build tool and dev server

### Backend Technologies
- **Flask**: Web framework
- **Flask-SocketIO**: WebSocket support
- **OpenCV**: Computer vision
- **face_recognition**: Face detection/recognition
- **Tesseract OCR**: Text extraction
- **Pillow**: Image processing
- **NumPy**: Numerical computations

## 📝 Configuration Files

### Frontend Configuration
- `package.json`: Dependencies, scripts, project metadata
- `vite.config.js`: Build tool configuration
- `eslint.config.js`: Code linting rules

### Backend Configuration
- `requirements.txt`: Python dependencies
- `.env`: Environment variables (SECRET_KEY, paths, etc.)
- `app.py`: Flask app configuration and routes

## 🔒 Security Considerations

### File Storage
- **Face encodings**: Stored as mathematical representations, not raw images
- **Temporary files**: Automatically cleaned up after processing
- **User data**: Encrypted and securely stored

### Access Control
- **Authentication**: Session-based user authentication
- **File access**: Restricted to authenticated users only
- **API endpoints**: Protected with proper validation

## 🚀 Deployment Structure

### Development
```
Local Development:
├── Frontend: http://localhost:5173 (Vite dev server)
├── Backend: http://localhost:5001 (Flask dev server)
└── WebSocket: ws://localhost:5001 (Socket.IO)
```

### Production
```
Production Deployment:
├── Frontend: Static files served by web server (nginx/Apache)
├── Backend: WSGI server (Gunicorn) behind reverse proxy
└── WebSocket: Socket.IO with production adapter
```

## 📈 Scalability Considerations

### Current Architecture
- **Single-server deployment**: Suitable for small to medium applications
- **File-based storage**: Simple but limited scalability
- **In-memory sessions**: Works for single-server deployments

### Future Enhancements
- **Database integration**: PostgreSQL/MongoDB for user data
- **Cloud storage**: AWS S3/Google Cloud for file storage
- **Load balancing**: Multiple server instances
- **Caching**: Redis for session management
- **CDN**: Content delivery for static assets

## 🔍 Monitoring and Logging

### Current Logging
- **Flask logging**: Application events and errors
- **Console output**: Development debugging
- **Error handling**: Try-catch blocks with logging

### Production Monitoring
- **Application logs**: Structured logging with timestamps
- **Error tracking**: Integration with error monitoring services
- **Performance metrics**: Response times and resource usage
- **Security logs**: Authentication attempts and access patterns
