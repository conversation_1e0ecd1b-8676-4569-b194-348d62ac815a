.pageContainer {
  font-family: Arial, sans-serif;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 70px;
  background-color: #000;
  color: #FFF;
}

.homeContainer {
  padding: 0;
  flex: 1;
}

.heroSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 70vh;
  padding: 2rem;
  background-color: #000;
  
  h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #CAFF33;
  }
  
  p {
    font-size: 1.2rem;
    max-width: 600px;
    margin-bottom: 2rem;
    color: #FFF;
  }
}

.ctaButton {
  padding: 0.8rem 2rem;
  background-color: #CAFF33;
  color: #000;
  border: none;
  border-radius: 4px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: darken(#CAFF33, 10%);
  }
}

.aboutSection, .howItWorksSection, .contactSection {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #000;
  
  h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
    color: #CAFF33;
  }
  
  p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #FFF;
    margin-bottom: 1.5rem;
  }
}

.howItWorksSection {
  background-color: #111;
}

.stepsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
  margin-top: 3rem;
}

.step {
  flex: 1;
  min-width: 250px;
  padding: 2rem;
  background-color: #111;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  text-align: center;
  
  h3 {
    margin: 1rem 0;
    color: #CAFF33;
  }
  
  p {
    font-size: 1rem;
    color: #FFF;
  }
}

.stepNumber {
  width: 40px;
  height: 40px;
  background-color: #CAFF33;
  color: #000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin: 0 auto;
}

.contactOptions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
  margin: 3rem 0;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.contactMethod {
  flex: 1;
  min-width: 250px;
  max-width: 300px;
  padding: 2rem;
  background-color: #111;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  }
  
  h3 {
    margin: 1rem 0;
    color: #CAFF33;
  }
  
  p {
    font-size: 1rem;
    color: #FFF;
    margin-bottom: 1rem;
  }
}

.contactIcon {
  width: 60px;
  height: 60px;
  background-color: rgba(202, 255, 51, 0.1);
  color: #CAFF33;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin: 0 auto;
}

.contactLink {
  display: inline-block;
  color: #CAFF33;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
  
  &:hover {
    color: darken(#CAFF33, 10%);
    text-decoration: underline;
  }
}

// Add proper scroll behavior and section positioning
html {
  scroll-behavior: smooth;
}

#about, #how-it-works, #contact {
  scroll-margin-top: 70px;
}

@media (max-width: 768px) {
  .heroSection {
    h1 {
      font-size: 2.5rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
  
  .stepsContainer {
    flex-direction: column;
  }
  
  .aboutSection, .howItWorksSection, .contactSection {
    h2 {
      font-size: 2rem;
    }
  }
}


