# Contributing to VerifID

Thank you for your interest in contributing to VerifID! We welcome contributions from the community and are grateful for any help you can provide.

## 🤝 How to Contribute

### Reporting Bugs

Before creating bug reports, please check the existing issues to avoid duplicates. When creating a bug report, include:

- **Clear title and description**
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Screenshots** if applicable
- **Environment details** (OS, browser, versions)
- **Error messages** or console logs

### Suggesting Features

Feature requests are welcome! Please provide:

- **Clear description** of the feature
- **Use case** and motivation
- **Possible implementation** approach
- **Alternative solutions** considered

### Code Contributions

1. **Fork the repository**
2. **Create a feature branch** from `main`
3. **Make your changes** following our coding standards
4. **Add tests** for new functionality
5. **Update documentation** as needed
6. **Submit a pull request**

## 🛠️ Development Setup

### Prerequisites
- Node.js (v16+)
- Python (3.8+)
- Git
- Tesseract OCR

### Local Development
```bash
# Clone your fork
git clone https://github.com/yourusername/VerifID.git
cd VerifID

# Backend setup
cd flask_backend
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt

# Frontend setup
cd ../frontend
npm install
```

## 📝 Coding Standards

### Python (Backend)
- Follow **PEP 8** style guide
- Use **type hints** where appropriate
- Write **docstrings** for functions and classes
- Keep functions **small and focused**
- Use **meaningful variable names**

```python
def process_face_encoding(image_path: str, user_id: str) -> Optional[np.ndarray]:
    """
    Process face encoding from an image file.
    
    Args:
        image_path: Path to the image file
        user_id: Unique identifier for the user
        
    Returns:
        Face encoding array or None if no face found
    """
    # Implementation here
```

### JavaScript/React (Frontend)
- Use **ES6+ features**
- Follow **React best practices**
- Use **functional components** with hooks
- Implement **proper error handling**
- Write **meaningful component names**

```javascript
const FaceVerification = ({ userId, onVerificationComplete }) => {
  const [status, setStatus] = useState('initializing');
  
  // Component implementation
};
```

### CSS/SCSS
- Use **modern Sass syntax** (`@use` instead of `@import`)
- Follow **BEM methodology** for class naming
- Use **CSS modules** for component styling
- Implement **responsive design**

```scss
@use "sass:color";

.verification-container {
  &__header {
    color: color.scale(#CAFF33, $lightness: -10%);
  }
}
```

## 🧪 Testing

### Backend Tests
```bash
cd flask_backend
python -m pytest tests/ -v
```

### Frontend Tests
```bash
cd frontend
npm test
```

### Test Coverage
- Aim for **80%+ test coverage**
- Write **unit tests** for utilities
- Write **integration tests** for API endpoints
- Write **component tests** for React components

## 📚 Documentation

### Code Documentation
- **Comment complex logic**
- **Update README** for new features
- **Document API changes**
- **Include examples** in docstrings

### Commit Messages
Follow conventional commit format:
```
type(scope): description

feat(auth): add password reset functionality
fix(face): resolve encoding issue with low-light images
docs(readme): update installation instructions
test(api): add tests for verification endpoints
```

## 🔍 Code Review Process

### Pull Request Guidelines
- **Clear title** describing the change
- **Detailed description** of what and why
- **Link related issues** using keywords (fixes #123)
- **Include screenshots** for UI changes
- **Ensure tests pass** and coverage is maintained

### Review Checklist
- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or properly documented)
- [ ] Security considerations addressed

## 🚀 Release Process

### Version Numbering
We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Steps
1. Update version numbers
2. Update CHANGELOG.md
3. Create release branch
4. Test thoroughly
5. Merge to main
6. Tag release
7. Deploy to production

## 🛡️ Security

### Security Guidelines
- **Never commit secrets** or API keys
- **Validate all inputs** on both frontend and backend
- **Use HTTPS** in production
- **Follow OWASP guidelines**
- **Report security issues** privately

### Reporting Security Issues
Please report security vulnerabilities to [<EMAIL>](mailto:<EMAIL>) instead of creating public issues.

## 📞 Getting Help

### Community Support
- **GitHub Discussions** for general questions
- **GitHub Issues** for bugs and feature requests
- **Discord/Slack** for real-time chat (if available)

### Maintainer Contact
- Create an issue for technical questions
- Email for security concerns
- Tag maintainers in PRs for review

## 🎉 Recognition

Contributors will be:
- **Listed in README.md**
- **Mentioned in release notes**
- **Invited to maintainer team** (for significant contributions)

Thank you for contributing to VerifID! 🙏
