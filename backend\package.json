{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"setup": "node setup.js", "start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "nodemon": "^3.1.10"}}