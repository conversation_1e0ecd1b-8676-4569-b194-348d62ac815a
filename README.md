# VerifID - AI-Powered Identity Verification System

<div align="center">
  <img src="frontend/public/logo.png" alt="VerifID Logo" width="200"/>
  
  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![React](https://img.shields.io/badge/React-19.0.0-blue.svg)](https://reactjs.org/)
  [![Flask](https://img.shields.io/badge/Flask-3.0.0-green.svg)](https://flask.palletsprojects.com/)
  [![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org/)
</div>

## 🚀 Overview

VerifID is a cutting-edge identity verification system that combines AI-powered document processing with real-time face liveness detection. The system provides secure, accurate, and user-friendly identity verification for modern applications.

### ✨ Key Features

- **🔍 AI-Powered OCR**: Advanced text extraction from ID documents using Tesseract OCR
- **👤 Face Recognition**: Real-time face detection and matching using face_recognition library
- **🎭 Liveness Detection**: Anti-spoofing protection with dynamic head movement verification
- **📱 Real-time Processing**: WebSocket-based live video processing
- **🔒 Secure Storage**: Encrypted user data and secure face encoding storage
- **📊 User Dashboard**: Comprehensive profile management and verification status
- **🌐 Modern UI**: Responsive React frontend with SCSS styling

## 🏗️ Architecture

```
VerifID/
├── frontend/          # React.js frontend application
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── pages/         # Application pages
│   │   ├── styles/        # SCSS stylesheets
│   │   └── utils/         # Utility functions
│   └── public/            # Static assets
├── flask_backend/     # Flask backend API
│   ├── app.py            # Main Flask application
│   ├── liveness_service.py # Face verification logic
│   ├── ocr_utils.py      # Document processing
│   └── requirements.txt   # Python dependencies
├── face_info/         # Stored face encodings
└── uploads/           # Temporary file storage
```

## 🛠️ Technology Stack

### Frontend
- **React 19.0.0** - Modern UI framework
- **React Router DOM 7.5.3** - Client-side routing
- **Socket.IO Client 4.8.1** - Real-time communication
- **Sass 1.87.0** - CSS preprocessing
- **Vite** - Fast build tool and dev server

### Backend
- **Flask 3.0.0** - Python web framework
- **Flask-SocketIO** - WebSocket support
- **OpenCV** - Computer vision processing
- **face_recognition** - Face detection and recognition
- **Tesseract OCR** - Text extraction from documents
- **Pillow** - Image processing
- **NumPy** - Numerical computations

## 📋 Prerequisites

Before setting up VerifID, ensure you have the following installed:

- **Node.js** (v16 or higher)
- **Python** (3.8 or higher)
- **Tesseract OCR** (for document text extraction)
- **Git** (for cloning the repository)

### Installing Tesseract OCR

#### Windows
```bash
# Download and install from: https://github.com/UB-Mannheim/tesseract/wiki
# Or using chocolatey:
choco install tesseract
```

#### macOS
```bash
brew install tesseract
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr
```

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/YOUR_USERNAME/VerifID.git
cd VerifID
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd flask_backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Start the Flask server
python app.py
```

The backend will start on `http://localhost:5001`

### 3. Frontend Setup
```bash
# Open new terminal and navigate to frontend
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

The frontend will start on `http://localhost:5173`

## 📖 How It Works

### 1. User Registration & Login
- Users create accounts with email and password
- Secure authentication with session management
- Profile management with personal information

### 2. ID Document Verification
- Users upload government-issued ID documents
- AI-powered OCR extracts text information
- Face detection isolates the photo from the document
- Extracted data is validated and stored securely

### 3. Face Liveness Verification
- Real-time webcam access for live verification
- Dynamic head movement commands (left, right, center)
- Anti-spoofing protection against photos/videos
- Face matching against stored ID document photo

### 4. Verification Results
- Instant feedback on verification status
- Confidence scores and detailed results
- Secure storage of verification history

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the backend directory:

```env
# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# File Upload Settings
MAX_CONTENT_LENGTH=********  # 16MB
UPLOAD_FOLDER=uploads

# Face Recognition Settings
FACE_RECOGNITION_TOLERANCE=0.55
LIVENESS_TIMEOUT=10

# OCR Settings
TESSERACT_PATH=/usr/bin/tesseract  # Adjust for your system
```

### Frontend Configuration
Update `frontend/src/config.js`:

```javascript
export const API_BASE_URL = 'http://localhost:5001';
export const SOCKET_URL = 'http://localhost:5001';
```

## 🧪 Testing

### Backend Tests
```bash
cd flask_backend
python -m pytest tests/
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 📦 Production Deployment

### Backend Deployment
```bash
# Install production dependencies
pip install gunicorn

# Run with Gunicorn
gunicorn --worker-class eventlet -w 1 --bind 0.0.0.0:5001 app:app
```

### Frontend Deployment
```bash
# Build for production
npm run build

# Serve static files (example with nginx)
# Copy dist/ contents to your web server
```

## 🔒 Security Features

- **Encrypted Data Storage**: All sensitive data is encrypted at rest
- **Secure Face Encodings**: Face data stored as mathematical encodings, not images
- **Anti-Spoofing**: Liveness detection prevents photo/video attacks
- **Session Management**: Secure user authentication and session handling
- **Input Validation**: Comprehensive validation of all user inputs

## 🤝 Contributing

We welcome contributions to VerifID! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow existing code style and conventions
- Add tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [Issues](https://github.com/YOUR_USERNAME/VerifID/issues) page
2. Create a new issue with detailed information
3. Contact the maintainers directly

## 🐛 Troubleshooting

### Common Issues

#### "Could not process reference face" Error
- Ensure face images are clear and well-lit
- Check that face_info directory exists and contains user face images
- Verify face_recognition library is properly installed

#### Webcam Access Issues
- Grant camera permissions in your browser
- Ensure no other applications are using the camera
- Try refreshing the page or restarting the browser

#### OCR Not Working
- Verify Tesseract is installed and in PATH
- Check document image quality and lighting
- Ensure supported document formats (JPG, PNG)

#### Build Errors
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check Node.js and Python versions match requirements
- Verify all dependencies are installed correctly

### Performance Optimization

- **Face Detection**: Adjust `face_detection_threshold` for better accuracy
- **Video Processing**: Modify frame processing rate in liveness detection
- **File Upload**: Optimize image compression before upload
- **Database**: Consider using a proper database for production

## 📊 API Documentation

### Authentication Endpoints
```
POST /api/auth/register    # User registration
POST /api/auth/login       # User login
POST /api/auth/logout      # User logout
GET  /api/auth/profile     # Get user profile
PUT  /api/auth/profile     # Update user profile
```

### Verification Endpoints
```
POST /api/verify/id                    # Upload ID document
POST /api/verify/face/initialize       # Start face verification
GET  /api/verify/face/check/{id}       # Check verification status
```

### WebSocket Events
```
start_liveness_check    # Initialize liveness detection
liveness_frame         # Send video frame for processing
liveness_instruction   # Receive movement instructions
liveness_feedback      # Receive real-time feedback
liveness_result        # Receive final verification result
liveness_error         # Receive error messages
```

## 🔄 Version History

### v1.0.0 (Current)
- Initial release with core functionality
- ID document verification with OCR
- Face liveness detection
- User authentication and profiles
- Real-time WebSocket communication

### Planned Features
- [ ] Multi-language OCR support
- [ ] Mobile app development
- [ ] Advanced anti-spoofing techniques
- [ ] Integration with external verification services
- [ ] Audit logging and compliance features

## 🙏 Acknowledgments

- [OpenCV](https://opencv.org/) for computer vision capabilities
- [face_recognition](https://github.com/ageitgey/face_recognition) for face detection
- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract) for text extraction
- [React](https://reactjs.org/) for the frontend framework
- [Flask](https://flask.palletsprojects.com/) for the backend framework
- [Socket.IO](https://socket.io/) for real-time communication

---

<div align="center">
  <p>Made with ❤️ by the VerifID Team</p>
  <p>
    <a href="https://github.com/YOUR_USERNAME/VerifID">⭐ Star this project</a> •
    <a href="https://github.com/YOUR_USERNAME/VerifID/issues">🐛 Report Bug</a> •
    <a href="https://github.com/YOUR_USERNAME/VerifID/issues">💡 Request Feature</a>
  </p>
</div>
