@echo off
setlocal enabledelayedexpansion

REM VerifID Setup Script for Windows
REM This script automates the setup process for the VerifID application

echo.
echo 🚀 VerifID Setup Script for Windows
echo ===================================
echo.

REM Check if Node.js is installed
echo [INFO] Checking prerequisites...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js v16 or higher.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js found: !NODE_VERSION!
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed. Please install npm.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo [SUCCESS] npm found: !NPM_VERSION!
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed. Please install Python 3.8 or higher.
    echo Download from: https://python.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo [SUCCESS] Python found: !PYTHON_VERSION!
)

REM Check if pip is installed
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] pip is not installed. Please install pip.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('pip --version') do set PIP_VERSION=%%i
    echo [SUCCESS] pip found: !PIP_VERSION!
)

REM Check if Tesseract is installed (optional)
tesseract --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Tesseract OCR not found. OCR functionality may not work.
    echo [WARNING] Please install Tesseract OCR for document processing.
    echo Download from: https://github.com/UB-Mannheim/tesseract/wiki
) else (
    for /f "tokens=*" %%i in ('tesseract --version') do set TESSERACT_VERSION=%%i
    echo [SUCCESS] Tesseract OCR found: !TESSERACT_VERSION!
)

echo.
echo [INFO] Setting up backend...

REM Setup backend
cd flask_backend

REM Create virtual environment
echo [INFO] Creating Python virtual environment...
python -m venv venv
if %errorlevel% neq 0 (
    echo [ERROR] Failed to create virtual environment.
    pause
    exit /b 1
)

REM Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

REM Install dependencies
echo [INFO] Installing Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Python dependencies.
    pause
    exit /b 1
)

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "uploads" mkdir uploads
if not exist "temp" mkdir temp
if not exist "..\face_info" mkdir ..\face_info

echo [SUCCESS] Backend setup completed!
cd ..

echo.
echo [INFO] Setting up frontend...

REM Setup frontend
cd frontend

REM Install dependencies
echo [INFO] Installing Node.js dependencies...
npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Node.js dependencies.
    pause
    exit /b 1
)

echo [SUCCESS] Frontend setup completed!
cd ..

echo.
echo [INFO] Creating environment configuration...

REM Create environment file
if not exist "flask_backend\.env" (
    (
        echo # Flask Configuration
        echo FLASK_ENV=development
        echo SECRET_KEY=your-secret-key-change-this-in-production
        echo.
        echo # File Upload Settings
        echo MAX_CONTENT_LENGTH=16777216
        echo UPLOAD_FOLDER=uploads
        echo.
        echo # Face Recognition Settings
        echo FACE_RECOGNITION_TOLERANCE=0.55
        echo LIVENESS_TIMEOUT=10
        echo.
        echo # OCR Settings
        echo TESSERACT_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe
    ) > flask_backend\.env
    echo [SUCCESS] Environment file created at flask_backend\.env
    echo [WARNING] Please update the SECRET_KEY in .env file for production use!
) else (
    echo [WARNING] Environment file already exists. Skipping creation.
)

echo.
echo [SUCCESS] 🎉 VerifID setup completed successfully!
echo.
echo [INFO] Next steps:
echo 1. Update flask_backend\.env with your configuration
echo 2. Start the backend: cd flask_backend ^&^& venv\Scripts\activate ^&^& python app.py
echo 3. Start the frontend: cd frontend ^&^& npm run dev
echo.
echo [INFO] The application will be available at:
echo - Frontend: http://localhost:5173
echo - Backend API: http://localhost:5001
echo.
echo [INFO] For more information, see README.md
echo.
pause
