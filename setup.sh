#!/bin/bash

# VerifID Setup Script
# This script automates the setup process for the VerifID application

set -e  # Exit on any error

echo "🚀 VerifID Setup Script"
echo "======================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js v16 or higher."
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python found: $PYTHON_VERSION"
    elif command_exists python; then
        PYTHON_VERSION=$(python --version)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check pip
    if command_exists pip3; then
        PIP_VERSION=$(pip3 --version)
        print_success "pip found: $PIP_VERSION"
    elif command_exists pip; then
        PIP_VERSION=$(pip --version)
        print_success "pip found: $PIP_VERSION"
    else
        print_error "pip is not installed. Please install pip."
        exit 1
    fi
    
    # Check Tesseract (optional but recommended)
    if command_exists tesseract; then
        TESSERACT_VERSION=$(tesseract --version | head -n1)
        print_success "Tesseract OCR found: $TESSERACT_VERSION"
    else
        print_warning "Tesseract OCR not found. OCR functionality may not work."
        print_warning "Please install Tesseract OCR for document processing."
    fi
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd flask_backend
    
    # Create virtual environment
    print_status "Creating Python virtual environment..."
    python3 -m venv venv 2>/dev/null || python -m venv venv
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate || source venv/Scripts/activate
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Create necessary directories
    print_status "Creating necessary directories..."
    mkdir -p uploads
    mkdir -p temp
    mkdir -p ../face_info
    
    print_success "Backend setup completed!"
    cd ..
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    print_success "Frontend setup completed!"
    cd ..
}

# Create environment file
create_env_file() {
    print_status "Creating environment configuration..."
    
    if [ ! -f flask_backend/.env ]; then
        cat > flask_backend/.env << EOF
# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-this-in-production

# File Upload Settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Face Recognition Settings
FACE_RECOGNITION_TOLERANCE=0.55
LIVENESS_TIMEOUT=10

# OCR Settings
TESSERACT_PATH=/usr/bin/tesseract
EOF
        print_success "Environment file created at flask_backend/.env"
        print_warning "Please update the SECRET_KEY in .env file for production use!"
    else
        print_warning "Environment file already exists. Skipping creation."
    fi
}

# Main setup function
main() {
    echo
    print_status "Starting VerifID setup process..."
    echo
    
    # Check prerequisites
    check_prerequisites
    echo
    
    # Setup backend
    setup_backend
    echo
    
    # Setup frontend
    setup_frontend
    echo
    
    # Create environment file
    create_env_file
    echo
    
    print_success "🎉 VerifID setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "1. Update flask_backend/.env with your configuration"
    echo "2. Start the backend: cd flask_backend && source venv/bin/activate && python app.py"
    echo "3. Start the frontend: cd frontend && npm run dev"
    echo
    print_status "The application will be available at:"
    echo "- Frontend: http://localhost:5173"
    echo "- Backend API: http://localhost:5001"
    echo
    print_status "For more information, see README.md"
}

# Run main function
main
